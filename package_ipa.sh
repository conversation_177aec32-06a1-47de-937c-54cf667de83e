#!/bin/bash

# SimLoc IPA Packaging Script
# Packages the improved SimLoc app with fixed location hook

set -e

APP_NAME="SimLoc"
APP_DIR="."
IPA_NAME="SimLoc_Fixed_v1.8.6.ipa"
PAYLOAD_DIR="Payload"
TEMP_DIR="/tmp/simloc_package"

echo "🚀 Starting SimLoc IPA packaging process..."

# Clean up any previous builds
if [ -d "$TEMP_DIR" ]; then
    rm -rf "$TEMP_DIR"
fi

if [ -f "$IPA_NAME" ]; then
    rm -f "$IPA_NAME"
fi

# Create temporary directory structure
mkdir -p "$TEMP_DIR/$PAYLOAD_DIR"

echo "📦 Copying app bundle..."
cp -R "$APP_DIR" "$TEMP_DIR/$PAYLOAD_DIR/$APP_NAME.app"

# Remove unnecessary files
echo "🧹 Cleaning up unnecessary files..."
find "$TEMP_DIR/$PAYLOAD_DIR/$APP_NAME.app" -name ".DS_Store" -delete
find "$TEMP_DIR/$PAYLOAD_DIR/$APP_NAME.app" -name "*.backup" -delete
rm -f "$TEMP_DIR/$PAYLOAD_DIR/$APP_NAME.app/Makefile"
rm -f "$TEMP_DIR/$PAYLOAD_DIR/$APP_NAME.app/LocationHook.m"
rm -f "$TEMP_DIR/$PAYLOAD_DIR/$APP_NAME.app/package_ipa.sh"

# Re-sign the app bundle
echo "✍️  Re-signing app bundle..."
codesign -f -s - --deep "$TEMP_DIR/$PAYLOAD_DIR/$APP_NAME.app"

# Re-sign the extension
if [ -d "$TEMP_DIR/$PAYLOAD_DIR/$APP_NAME.app/PlugIns/TAAction.appex" ]; then
    echo "✍️  Re-signing app extension..."
    codesign -f -s - "$TEMP_DIR/$PAYLOAD_DIR/$APP_NAME.app/PlugIns/TAAction.appex"
fi

# Re-sign the dynamic library
if [ -f "$TEMP_DIR/$PAYLOAD_DIR/$APP_NAME.app/macked.app.dylib" ]; then
    echo "✍️  Re-signing dynamic library..."
    codesign -f -s - "$TEMP_DIR/$PAYLOAD_DIR/$APP_NAME.app/macked.app.dylib"
fi

# Create the IPA
echo "📱 Creating IPA file..."
cd "$TEMP_DIR"
zip -r "../$IPA_NAME" "$PAYLOAD_DIR"
cd - > /dev/null

# Move IPA to current directory
mv "$TEMP_DIR/../$IPA_NAME" "./"

# Clean up
rm -rf "$TEMP_DIR"

echo "✅ IPA packaging completed successfully!"
echo "📄 Output file: $IPA_NAME"
echo ""
echo "🔧 Improvements in this version:"
echo "   • Fixed deadlock issues in location simulation"
echo "   • Thread-safe location hook implementation"
echo "   • Improved error handling and stability"
echo "   • Async processing to prevent UI blocking"
echo "   • Better compatibility with various apps"
echo ""
echo "📋 Installation instructions:"
echo "   1. Install using AltStore, Sideloadly, or similar tools"
echo "   2. Trust the developer certificate in Settings > General > VPN & Device Management"
echo "   3. The app should now work without causing target apps to freeze"
echo ""

# Verify the IPA was created
if [ -f "$IPA_NAME" ]; then
    FILE_SIZE=$(ls -lh "$IPA_NAME" | awk '{print $5}')
    echo "📊 IPA file size: $FILE_SIZE"
    echo "🎉 Ready for installation!"
else
    echo "❌ Error: IPA file was not created successfully"
    exit 1
fi
