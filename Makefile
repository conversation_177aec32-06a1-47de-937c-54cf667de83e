# Makefile for improved LocationHook dynamic library
# Fixes deadlock issues in location simulation

CC = clang
CFLAGS = -arch arm64 -isysroot $(shell xcrun --show-sdk-path --sdk iphoneos) \
         -miphoneos-version-min=14.0 -fobjc-arc -fPIC -shared \
         -framework Foundation -framework CoreLocation \
         -O2 -fvisibility=hidden

TARGET = macked.app.dylib
SOURCE = LocationHook.m
BACKUP = macked.app.dylib.backup

.PHONY: all clean backup restore install

all: backup $(TARGET)

backup:
	@if [ -f $(TARGET) ]; then \
		echo "Creating backup of original dylib..."; \
		cp $(TARGET) $(BACKUP); \
	fi

$(TARGET): $(SOURCE)
	@echo "Compiling improved location hook..."
	$(CC) $(CFLAGS) -o $(TARGET) $(SOURCE)
	@echo "Signing the dylib..."
	codesign -f -s - $(TARGET)
	@echo "Build completed successfully!"

clean:
	rm -f $(TARGET)

restore:
	@if [ -f $(BACKUP) ]; then \
		echo "Restoring original dylib..."; \
		cp $(BACKUP) $(TARGET); \
		echo "Original dylib restored."; \
	else \
		echo "No backup found."; \
	fi

install: all
	@echo "Installing improved location hook..."
	@echo "The improved dylib has been built and is ready to use."
	@echo "Key improvements:"
	@echo "  - Thread-safe implementation"
	@echo "  - Deadlock prevention"
	@echo "  - Proper error handling"
	@echo "  - Async processing to avoid UI blocking"

help:
	@echo "Available targets:"
	@echo "  all      - Build the improved dylib (default)"
	@echo "  backup   - Create backup of original dylib"
	@echo "  restore  - Restore original dylib from backup"
	@echo "  clean    - Remove built files"
	@echo "  install  - Build and install the improved dylib"
	@echo "  help     - Show this help message"
