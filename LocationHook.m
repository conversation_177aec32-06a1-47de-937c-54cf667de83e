//
//  LocationHook.m
//  SimLoc - Improved Location Hook
//
//  Created by Augment Agent
//  Copyright © 2024 SimLoc. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <CoreLocation/CoreLocation.h>
#import <objc/runtime.h>
#import <dispatch/dispatch.h>

// 全局变量存储模拟位置
static CLLocationCoordinate2D g_fakeCoordinate = {0.0, 0.0};
static BOOL g_locationHookEnabled = NO;
static dispatch_queue_t g_locationQueue = nil;
static NSLock *g_locationLock = nil;

// 原始方法指针
static CLLocation* (*original_location)(id, SEL) = NULL;
static CLLocationCoordinate2D (*original_coordinate)(id, SEL) = NULL;

// 安全的Method Swizzling实现
void safe_method_swizzle(Class cls, SEL originalSelector, SEL swizzledSelector) {
    Method originalMethod = class_getInstanceMethod(cls, originalSelector);
    Method swizzledMethod = class_getInstanceMethod(cls, swizzledSelector);
    
    if (!originalMethod || !swizzledMethod) {
        NSLog(@"[LocationHook] Warning: Method not found for swizzling");
        return;
    }
    
    BOOL didAddMethod = class_addMethod(cls,
                                        originalSelector,
                                        method_getImplementation(swizzledMethod),
                                        method_getTypeEncoding(swizzledMethod));
    
    if (didAddMethod) {
        class_replaceMethod(cls,
                           swizzledSelector,
                           method_getImplementation(originalMethod),
                           method_getTypeEncoding(originalMethod));
    } else {
        method_exchangeImplementations(originalMethod, swizzledMethod);
    }
}

// 线程安全的位置设置
void setFakeLocation(double latitude, double longitude) {
    if (!g_locationLock) return;
    
    [g_locationLock lock];
    g_fakeCoordinate.latitude = latitude;
    g_fakeCoordinate.longitude = longitude;
    g_locationHookEnabled = (latitude != 0.0 || longitude != 0.0);
    [g_locationLock unlock];
}

// 线程安全的位置获取
CLLocationCoordinate2D getFakeLocation() {
    if (!g_locationLock) return CLLocationCoordinate2DMake(0, 0);
    
    [g_locationLock lock];
    CLLocationCoordinate2D coord = g_fakeCoordinate;
    [g_locationLock unlock];
    
    return coord;
}

@interface CLLocationManager (LocationHook)
- (CLLocation *)hooked_location;
@end

@interface CLLocation (LocationHook)
- (CLLocationCoordinate2D)hooked_coordinate;
@end

@implementation CLLocationManager (LocationHook)

- (CLLocation *)hooked_location {
    if (!g_locationHookEnabled) {
        // 如果没有启用hook，调用原始方法
        if (original_location) {
            return original_location(self, @selector(location));
        } else {
            return [self hooked_location]; // 调用原始实现
        }
    }
    
    // 异步处理，避免阻塞主线程
    __block CLLocation *result = nil;
    
    if (g_locationQueue) {
        dispatch_sync(g_locationQueue, ^{
            CLLocationCoordinate2D fakeCoord = getFakeLocation();
            
            if (CLLocationCoordinate2DIsValid(fakeCoord)) {
                result = [[CLLocation alloc] initWithLatitude:fakeCoord.latitude
                                                    longitude:fakeCoord.longitude];
            } else {
                // 如果坐标无效，返回原始位置
                if (original_location) {
                    result = original_location(self, @selector(location));
                } else {
                    result = [self hooked_location];
                }
            }
        });
    }
    
    return result;
}

@end

@implementation CLLocation (LocationHook)

- (CLLocationCoordinate2D)hooked_coordinate {
    if (!g_locationHookEnabled) {
        // 如果没有启用hook，调用原始方法
        if (original_coordinate) {
            return original_coordinate(self, @selector(coordinate));
        } else {
            return [self hooked_coordinate]; // 调用原始实现
        }
    }
    
    CLLocationCoordinate2D fakeCoord = getFakeLocation();
    
    if (CLLocationCoordinate2DIsValid(fakeCoord)) {
        return fakeCoord;
    } else {
        // 如果坐标无效，返回原始坐标
        if (original_coordinate) {
            return original_coordinate(self, @selector(coordinate));
        } else {
            return [self hooked_coordinate];
        }
    }
}

@end

// 导出函数供外部调用
__attribute__((visibility("default")))
void enableLocationHook(double latitude, double longitude) {
    setFakeLocation(latitude, longitude);
}

__attribute__((visibility("default")))
void disableLocationHook() {
    setFakeLocation(0.0, 0.0);
}

// 构造函数 - 在库加载时自动执行
__attribute__((constructor))
static void initLocationHook() {
    @autoreleasepool {
        NSLog(@"[LocationHook] Initializing improved location hook...");
        
        // 初始化线程安全组件
        g_locationQueue = dispatch_queue_create("com.simloc.location.queue", DISPATCH_QUEUE_SERIAL);
        g_locationLock = [[NSLock alloc] init];
        
        // 保存原始方法指针
        Method originalLocationMethod = class_getInstanceMethod([CLLocationManager class], @selector(location));
        if (originalLocationMethod) {
            original_location = (CLLocation* (*)(id, SEL))method_getImplementation(originalLocationMethod);
        }
        
        Method originalCoordinateMethod = class_getInstanceMethod([CLLocation class], @selector(coordinate));
        if (originalCoordinateMethod) {
            original_coordinate = (CLLocationCoordinate2D (*)(id, SEL))method_getImplementation(originalCoordinateMethod);
        }
        
        // 执行安全的Method Swizzling
        safe_method_swizzle([CLLocationManager class], 
                           @selector(location), 
                           @selector(hooked_location));
        
        safe_method_swizzle([CLLocation class], 
                           @selector(coordinate), 
                           @selector(hooked_coordinate));
        
        NSLog(@"[LocationHook] Location hook initialized successfully");
    }
}

// 析构函数 - 在库卸载时执行清理
__attribute__((destructor))
static void cleanupLocationHook() {
    NSLog(@"[LocationHook] Cleaning up location hook...");
    
    if (g_locationQueue) {
        g_locationQueue = nil;
    }
    
    g_locationLock = nil;
    g_locationHookEnabled = NO;
}
