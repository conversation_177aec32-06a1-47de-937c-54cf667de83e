#!/bin/bash

# SimLoc 混合版本创建脚本
# 将IPA的修复组件移植到Shantui的新版本中

set -e

SHANTUI_DIR="/Users/<USER>/Desktop/shantui"
IPA_EXTRACT_DIR="/tmp/ipa_compare/Payload/SimLoc.app"
HYBRID_DIR="/tmp/simloc_hybrid"
HYBRID_IPA="SimLoc_Hybrid_v1.9.6_Fixed.ipa"

echo "🚀 开始创建SimLoc混合版本..."
echo "📋 版本信息: 1.9.6 (Shantu<PERSON>) + 修复组件 (IPA)"

# 检查源文件夹是否存在
if [ ! -d "$SHANTUI_DIR" ]; then
    echo "❌ 错误: Shantui文件夹不存在: $SHANTUI_DIR"
    exit 1
fi

if [ ! -d "$IPA_EXTRACT_DIR" ]; then
    echo "❌ 错误: IPA解压文件夹不存在: $IPA_EXTRACT_DIR"
    echo "请先运行对比脚本解压IPA文件"
    exit 1
fi

# 清理并创建工作目录
echo "🧹 清理工作目录..."
rm -rf "$HYBRID_DIR"
mkdir -p "$HYBRID_DIR/Payload"

# 复制Shantui版本作为基础
echo "📦 复制Shantui版本作为基础..."
cp -R "$SHANTUI_DIR" "$HYBRID_DIR/Payload/SimLoc.app"

# 检查并复制关键的修复组件
echo "🔧 添加修复组件..."

# 1. 复制修复的动态库
if [ -f "$IPA_EXTRACT_DIR/macked.app.dylib" ]; then
    echo "  ✅ 添加修复的动态库: macked.app.dylib"
    cp "$IPA_EXTRACT_DIR/macked.app.dylib" "$HYBRID_DIR/Payload/SimLoc.app/"
else
    echo "  ❌ 警告: 未找到动态库文件"
fi

# 2. 复制扩展插件
if [ -d "$IPA_EXTRACT_DIR/PlugIns" ]; then
    echo "  ✅ 添加扩展插件: PlugIns/"
    cp -R "$IPA_EXTRACT_DIR/PlugIns" "$HYBRID_DIR/Payload/SimLoc.app/"
else
    echo "  ❌ 警告: 未找到PlugIns文件夹"
fi

# 3. 更新版本信息，保持Shantui的版本号但添加修复标识
echo "📝 更新版本信息..."
if [ -f "$HYBRID_DIR/Payload/SimLoc.app/Info.plist" ]; then
    # 使用plutil修改版本信息
    plutil -replace CFBundleShortVersionString -string "1.9.6-Fixed" "$HYBRID_DIR/Payload/SimLoc.app/Info.plist"
    plutil -replace CFBundleIdentifier -string "com.simloc.app.hybrid" "$HYBRID_DIR/Payload/SimLoc.app/Info.plist"
    echo "  ✅ 版本号更新为: 1.9.6-Fixed"
    echo "  ✅ Bundle ID更新为: com.simloc.app.hybrid"
fi

# 4. 清理不需要的文件
echo "🧹 清理不需要的文件..."
find "$HYBRID_DIR/Payload/SimLoc.app" -name ".DS_Store" -delete 2>/dev/null || true
rm -f "$HYBRID_DIR/Payload/SimLoc.app/icon_resource.dat" 2>/dev/null || true
rm -f "$HYBRID_DIR/Payload/SimLoc.app/icon_resource.size" 2>/dev/null || true

# 5. 重新签名所有组件
echo "✍️  重新签名应用组件..."

# 签名扩展插件
if [ -d "$HYBRID_DIR/Payload/SimLoc.app/PlugIns/TAAction.appex" ]; then
    codesign -f -s - "$HYBRID_DIR/Payload/SimLoc.app/PlugIns/TAAction.appex"
    echo "  ✅ 扩展插件已签名"
fi

# 签名动态库
if [ -f "$HYBRID_DIR/Payload/SimLoc.app/macked.app.dylib" ]; then
    codesign -f -s - "$HYBRID_DIR/Payload/SimLoc.app/macked.app.dylib"
    echo "  ✅ 动态库已签名"
fi

# 签名主应用
codesign -f -s - --deep "$HYBRID_DIR/Payload/SimLoc.app"
echo "  ✅ 主应用已签名"

# 6. 创建IPA文件
echo "📱 创建混合版本IPA..."
cd "$HYBRID_DIR"
zip -r "../$HYBRID_IPA" Payload
cd - > /dev/null

# 移动到当前目录
mv "/tmp/$HYBRID_IPA" "./"

# 清理临时文件
rm -rf "$HYBRID_DIR"

echo ""
echo "🎉 混合版本创建完成！"
echo "📄 输出文件: $HYBRID_IPA"

# 验证文件
if [ -f "$HYBRID_IPA" ]; then
    FILE_SIZE=$(ls -lh "$HYBRID_IPA" | awk '{print $5}')
    echo "📊 文件大小: $FILE_SIZE"
    echo ""
    echo "🔧 混合版本特性:"
    echo "   ✅ 基于Shantui 1.9.6版本"
    echo "   ✅ 包含修复的动态库 (解决卡死问题)"
    echo "   ✅ 包含完整的扩展插件"
    echo "   ✅ 线程安全的位置hook实现"
    echo "   ✅ 最新功能 + 稳定性修复"
    echo ""
    echo "📋 安装说明:"
    echo "   1. 使用AltStore、Sideloadly等工具安装"
    echo "   2. 在设置中信任开发者证书"
    echo "   3. 享受最新版本的稳定体验"
    echo ""
    echo "🎯 这个版本结合了两者的优势:"
    echo "   • Shantui的新版本功能 (1.9.6)"
    echo "   • IPA的稳定性修复"
    echo "   • 完整的模拟定位功能"
else
    echo "❌ 错误: 混合版本创建失败"
    exit 1
fi
