
/* Class = "UIButton"; configuration.title = "App"; ObjectID = "2rF-Oi-gCW"; */
"2rF-Oi-gCW.configuration.title" = "App";

/* Class = "UIButton"; normalTitle = "Button"; ObjectID = "2rF-Oi-gCW"; */
"2rF-Oi-gCW.normalTitle" = "App";

/* Class = "UILabel"; text = "23.0,106.3"; ObjectID = "HL3-h9-h5G"; */
"HL3-h9-h5G.text" = "23.0,106.3";

/* Class = "UITextField"; placeholder = "29.9999"; ObjectID = "MYT-bT-svD"; */
"MYT-bT-svD.placeholder" = "29.9999";

/* Class = "UITextView"; text = "Lorem ipsum dolor sit er elit lamet, consectetaur cillium adipisicing pecu, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum. Nam liber te conscient to factor tum poen legum odioque civiuda."; ObjectID = "UGP-jc-pj6"; */
"UGP-jc-pj6.text" = "Lorem ipsum dolor sit er elit lamet, consectetaur cillium adipisicing pecu, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum. Nam liber te conscient to factor tum poen legum odioque civiuda.";

/* Class = "UILabel"; text = "地址"; ObjectID = "UaD-V1-Ak8"; */
"UaD-V1-Ak8.text" = "Address";

/* Class = "UIButton"; configuration.title = "地图选点"; ObjectID = "Vw5-IX-peB"; */
"Vw5-IX-peB.configuration.title" = "Maps";

/* Class = "UIButton"; normalTitle = "Button"; ObjectID = "Vw5-IX-peB"; */
"Vw5-IX-peB.normalTitle" = "Maps";

/* Class = "UIButton"; configuration.title = "模拟定位"; ObjectID = "bnH-x2-4OQ"; */
"bnH-x2-4OQ.configuration.title" = "License";

/* Class = "UIButton"; normalTitle = "Button"; ObjectID = "bnH-x2-4OQ"; */
"bnH-x2-4OQ.normalTitle" = "License";

/* Class = "UIButton"; configuration.title = "连接Wi-Fi"; ObjectID = "dyN-Hv-TQS"; */
"dyN-Hv-TQS.configuration.title" = "Custom Wi-Fi";

/* Class = "UIButton"; normalTitle = "Button"; ObjectID = "dyN-Hv-TQS"; */
"dyN-Hv-TQS.normalTitle" = "Custom Wi-Fi";

/* Class = "UILabel"; text = "北京市朝阳区"; ObjectID = "euc-tN-vNK"; */
"euc-tN-vNK.text" = "Beijing";

/* Class = "UILabel"; text = "fakewifi"; ObjectID = "hZT-ea-Tlk"; */
"hZT-ea-Tlk.text" = "fakewifi";

/* Class = "UIButton"; configuration.title = "历史Wi-Fi"; ObjectID = "iKA-Z7-rTl"; */
"iKA-Z7-rTl.configuration.title" = "Wi-Fi List";

/* Class = "UIButton"; normalTitle = "Button"; ObjectID = "iKA-Z7-rTl"; */
"iKA-Z7-rTl.normalTitle" = "Wi-Fi List";

/* Class = "UITextField"; placeholder = "106.66666"; ObjectID = "qtq-cP-88p"; */
"qtq-cP-88p.placeholder" = "106.66666";

/* Class = "UILabel"; text = "Wi-Fi"; ObjectID = "wEX-6j-RWG"; */
"wEX-6j-RWG.text" = "Wi-Fi";

/* Class = "UILabel"; text = "经纬度"; ObjectID = "xtL-db-R43"; */
"xtL-db-R43.text" = "Coord";

"shL-go-3y5.text" = "Not Set";

"j69-ed-LTY.title" = "Location";
