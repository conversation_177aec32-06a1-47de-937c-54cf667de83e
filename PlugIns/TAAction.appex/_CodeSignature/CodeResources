<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key><EMAIL></key>
		<data>
		Nb6FY0A+bSskJaOdU8NapvCMTbg=
		</data>
		<key>AppIcon20x20@2x~ipad.png</key>
		<data>
		Nb6FY0A+bSskJaOdU8NapvCMTbg=
		</data>
		<key><EMAIL></key>
		<data>
		rHENCkJTlqVayijXiPHHok80RD8=
		</data>
		<key>AppIcon20x20~ipad.png</key>
		<data>
		oKBW8VSWtvtMxudxVML+4kEbX0M=
		</data>
		<key>AppIcon29x29.png</key>
		<data>
		TiLcn2wWykB6NBSit/803bbx+bE=
		</data>
		<key><EMAIL></key>
		<data>
		YmHPOZOVSfjGu7xJLWHriPRG9rw=
		</data>
		<key>AppIcon29x29@2x~ipad.png</key>
		<data>
		YmHPOZOVSfjGu7xJLWHriPRG9rw=
		</data>
		<key><EMAIL></key>
		<data>
		4p0WG1k4WyAc59YTThtw0dvUiv0=
		</data>
		<key>AppIcon29x29~ipad.png</key>
		<data>
		TiLcn2wWykB6NBSit/803bbx+bE=
		</data>
		<key><EMAIL></key>
		<data>
		gxW8LUcgwsN3GDtTAfExHt6CQl4=
		</data>
		<key>AppIcon40x40@2x~ipad.png</key>
		<data>
		gxW8LUcgwsN3GDtTAfExHt6CQl4=
		</data>
		<key><EMAIL></key>
		<data>
		/s43o9LtyInPGVMgPI7GMv5iUDQ=
		</data>
		<key>AppIcon40x40~ipad.png</key>
		<data>
		Nb6FY0A+bSskJaOdU8NapvCMTbg=
		</data>
		<key>AppIcon50x50@2x~ipad.png</key>
		<data>
		fCNNa01TeAf+QWOuqpMi13p7LCc=
		</data>
		<key>AppIcon50x50~ipad.png</key>
		<data>
		cN0SfYmxDBDzaRM+FsqjascGYig=
		</data>
		<key>AppIcon57x57.png</key>
		<data>
		cjpPLjkO3a3QWH5eFUNFjHUW8uM=
		</data>
		<key><EMAIL></key>
		<data>
		AZGATvmTXwmdIzci+aVaYQDo+V8=
		</data>
		<key><EMAIL></key>
		<data>
		/s43o9LtyInPGVMgPI7GMv5iUDQ=
		</data>
		<key><EMAIL></key>
		<data>
		sEbiksDvI5IS+uBkXcwPBQB7dxo=
		</data>
		<key>AppIcon72x72@2x~ipad.png</key>
		<data>
		70gRnJKHolWEt2r44Jtxno5/WOk=
		</data>
		<key>AppIcon72x72~ipad.png</key>
		<data>
		ht8NuExK4vz+uFgkgo4C5pf5ghg=
		</data>
		<key>AppIcon76x76@2x~ipad.png</key>
		<data>
		3FbKZOdp1ThHUcmTUS8pk39HHLk=
		</data>
		<key>AppIcon76x76~ipad.png</key>
		<data>
		ADxNtKNHi+p6A1JdMptP++IxcoQ=
		</data>
		<key>AppIcon83.5x83.5@2x~ipad.png</key>
		<data>
		5CEggOTBqxE9zb1uC1GmgMj9eY0=
		</data>
		<key>Assets.car</key>
		<data>
		/o6Tbm4iR4pmI4a5oBcDpw5Zf5s=
		</data>
		<key>Base.lproj/MainInterface.storyboardc/Info.plist</key>
		<data>
		LphSzypO58Ee41BVLe8F3DJZUps=
		</data>
		<key>Base.lproj/MainInterface.storyboardc/ObA-dk-sSI-view-zMn-AG-sqS.nib</key>
		<data>
		0IjWpysJaGkLVF9oDzzXtVJ9ktU=
		</data>
		<key>Base.lproj/MainInterface.storyboardc/UIViewController-ObA-dk-sSI.nib</key>
		<data>
		eB7HT+rTuqAbLBK/aoll4ugnL/o=
		</data>
		<key>Info.plist</key>
		<data>
		xvKMODuDa+3t8oaDlKMB7wQxWC8=
		</data>
		<key>en.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			l91pc11VZkWXN5RfVh4tYkinNN0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>en.lproj/MainInterface.strings</key>
		<dict>
			<key>hash</key>
			<data>
			BdnZmPuY2unYFbzwR6aEhWiD0LA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hans.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+J/SFbMCq8czy5xL/jdTbnoBjC8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hans.lproj/MainInterface.strings</key>
		<dict>
			<key>hash</key>
			<data>
			BdnZmPuY2unYFbzwR6aEhWiD0LA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>files2</key>
	<dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			Nb6FY0A+bSskJaOdU8NapvCMTbg=
			</data>
			<key>hash2</key>
			<data>
			kfGV5toF0b660WRhEddj3nWty6WRoCPdhh32TJKDfvs=
			</data>
		</dict>
		<key>AppIcon20x20@2x~ipad.png</key>
		<dict>
			<key>hash</key>
			<data>
			Nb6FY0A+bSskJaOdU8NapvCMTbg=
			</data>
			<key>hash2</key>
			<data>
			kfGV5toF0b660WRhEddj3nWty6WRoCPdhh32TJKDfvs=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			rHENCkJTlqVayijXiPHHok80RD8=
			</data>
			<key>hash2</key>
			<data>
			hDlnSbpJzo0mJ4vxq0pB0mLFNYpFgrx7HuT75OIEscY=
			</data>
		</dict>
		<key>AppIcon20x20~ipad.png</key>
		<dict>
			<key>hash</key>
			<data>
			oKBW8VSWtvtMxudxVML+4kEbX0M=
			</data>
			<key>hash2</key>
			<data>
			79K+LgFsFnrjpaPukI8LwA72bAUQpXANbNpmCvAFv8g=
			</data>
		</dict>
		<key>AppIcon29x29.png</key>
		<dict>
			<key>hash</key>
			<data>
			TiLcn2wWykB6NBSit/803bbx+bE=
			</data>
			<key>hash2</key>
			<data>
			B9GZ6rsBJ9/cwYgd7m3Kj6T5/IQt0qNi4ABZkhUc0R0=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			YmHPOZOVSfjGu7xJLWHriPRG9rw=
			</data>
			<key>hash2</key>
			<data>
			Z0lUiyNCFFPZ8MCsRs3kJ5EoNGc5us29GVsjrjbwjKQ=
			</data>
		</dict>
		<key>AppIcon29x29@2x~ipad.png</key>
		<dict>
			<key>hash</key>
			<data>
			YmHPOZOVSfjGu7xJLWHriPRG9rw=
			</data>
			<key>hash2</key>
			<data>
			Z0lUiyNCFFPZ8MCsRs3kJ5EoNGc5us29GVsjrjbwjKQ=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			4p0WG1k4WyAc59YTThtw0dvUiv0=
			</data>
			<key>hash2</key>
			<data>
			cNNBymOmHlrszVkZgHBpW73RGMBoiga/K3qfcjZSbwg=
			</data>
		</dict>
		<key>AppIcon29x29~ipad.png</key>
		<dict>
			<key>hash</key>
			<data>
			TiLcn2wWykB6NBSit/803bbx+bE=
			</data>
			<key>hash2</key>
			<data>
			B9GZ6rsBJ9/cwYgd7m3Kj6T5/IQt0qNi4ABZkhUc0R0=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			gxW8LUcgwsN3GDtTAfExHt6CQl4=
			</data>
			<key>hash2</key>
			<data>
			zeBXAgr51pBAjOUyiLbSXW4rdt94QjwaePOSSGN9MoI=
			</data>
		</dict>
		<key>AppIcon40x40@2x~ipad.png</key>
		<dict>
			<key>hash</key>
			<data>
			gxW8LUcgwsN3GDtTAfExHt6CQl4=
			</data>
			<key>hash2</key>
			<data>
			zeBXAgr51pBAjOUyiLbSXW4rdt94QjwaePOSSGN9MoI=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			/s43o9LtyInPGVMgPI7GMv5iUDQ=
			</data>
			<key>hash2</key>
			<data>
			8dfxyVLFscN+7gIDw/wLFv9qd565n84m4prOvQ+5VCA=
			</data>
		</dict>
		<key>AppIcon40x40~ipad.png</key>
		<dict>
			<key>hash</key>
			<data>
			Nb6FY0A+bSskJaOdU8NapvCMTbg=
			</data>
			<key>hash2</key>
			<data>
			kfGV5toF0b660WRhEddj3nWty6WRoCPdhh32TJKDfvs=
			</data>
		</dict>
		<key>AppIcon50x50@2x~ipad.png</key>
		<dict>
			<key>hash</key>
			<data>
			fCNNa01TeAf+QWOuqpMi13p7LCc=
			</data>
			<key>hash2</key>
			<data>
			ksUeor/lSPhOKX7CODEN8eapSHquI5QCkA230wobrLw=
			</data>
		</dict>
		<key>AppIcon50x50~ipad.png</key>
		<dict>
			<key>hash</key>
			<data>
			cN0SfYmxDBDzaRM+FsqjascGYig=
			</data>
			<key>hash2</key>
			<data>
			4c8w0PDU8mG9OopdEebxJayGiC8ykDokaAuSb/o0Zxo=
			</data>
		</dict>
		<key>AppIcon57x57.png</key>
		<dict>
			<key>hash</key>
			<data>
			cjpPLjkO3a3QWH5eFUNFjHUW8uM=
			</data>
			<key>hash2</key>
			<data>
			/l9lUo4SAtAgF458m3fIFinwOYmqki1G+NNljC1KzTs=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			AZGATvmTXwmdIzci+aVaYQDo+V8=
			</data>
			<key>hash2</key>
			<data>
			eMd2xnkRx9jwSewX1qPX7ptOrygK5r1I+nIo+HhvX0s=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			/s43o9LtyInPGVMgPI7GMv5iUDQ=
			</data>
			<key>hash2</key>
			<data>
			8dfxyVLFscN+7gIDw/wLFv9qd565n84m4prOvQ+5VCA=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			sEbiksDvI5IS+uBkXcwPBQB7dxo=
			</data>
			<key>hash2</key>
			<data>
			O268Sj5bS8tUCXt62WtVBkOjNo7hQpAiAXRC6jVKH18=
			</data>
		</dict>
		<key>AppIcon72x72@2x~ipad.png</key>
		<dict>
			<key>hash</key>
			<data>
			70gRnJKHolWEt2r44Jtxno5/WOk=
			</data>
			<key>hash2</key>
			<data>
			BS/9OYj5HTn7dPAewCxX8mYEa+vJFmTMgJLWyeEsl64=
			</data>
		</dict>
		<key>AppIcon72x72~ipad.png</key>
		<dict>
			<key>hash</key>
			<data>
			ht8NuExK4vz+uFgkgo4C5pf5ghg=
			</data>
			<key>hash2</key>
			<data>
			Y5Nu1nV8E/Rcmz8f5P5Ts66Lhpq80KfUKwZ1mpi6nF0=
			</data>
		</dict>
		<key>AppIcon76x76@2x~ipad.png</key>
		<dict>
			<key>hash</key>
			<data>
			3FbKZOdp1ThHUcmTUS8pk39HHLk=
			</data>
			<key>hash2</key>
			<data>
			pd6jc2toSvCEfXhJCgmBvZvX32GdhV86Cg50Q7tKGjc=
			</data>
		</dict>
		<key>AppIcon76x76~ipad.png</key>
		<dict>
			<key>hash</key>
			<data>
			ADxNtKNHi+p6A1JdMptP++IxcoQ=
			</data>
			<key>hash2</key>
			<data>
			I/AXLxtWfWpL9qajZJM5gfR+nMVKwFljbsTMagjDSyw=
			</data>
		</dict>
		<key>AppIcon83.5x83.5@2x~ipad.png</key>
		<dict>
			<key>hash</key>
			<data>
			5CEggOTBqxE9zb1uC1GmgMj9eY0=
			</data>
			<key>hash2</key>
			<data>
			PsorPREleNrd8qDloTTWYkhByqQkC3lW3HTx1x7/ra4=
			</data>
		</dict>
		<key>Assets.car</key>
		<dict>
			<key>hash</key>
			<data>
			/o6Tbm4iR4pmI4a5oBcDpw5Zf5s=
			</data>
			<key>hash2</key>
			<data>
			KnImrGMza6SG6i06trB4XsQZd2w2H4sQLmWh+n7O8uA=
			</data>
		</dict>
		<key>Base.lproj/MainInterface.storyboardc/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			LphSzypO58Ee41BVLe8F3DJZUps=
			</data>
			<key>hash2</key>
			<data>
			9Zkoj+a7Nv+GT39rrbg05J2Y7OQHhbaZkFLZo44H0so=
			</data>
		</dict>
		<key>Base.lproj/MainInterface.storyboardc/ObA-dk-sSI-view-zMn-AG-sqS.nib</key>
		<dict>
			<key>hash</key>
			<data>
			0IjWpysJaGkLVF9oDzzXtVJ9ktU=
			</data>
			<key>hash2</key>
			<data>
			bzXaRcFvlbhZQxUCWojp3BdTYV5z2jwuQ6OZY2MtWR4=
			</data>
		</dict>
		<key>Base.lproj/MainInterface.storyboardc/UIViewController-ObA-dk-sSI.nib</key>
		<dict>
			<key>hash</key>
			<data>
			eB7HT+rTuqAbLBK/aoll4ugnL/o=
			</data>
			<key>hash2</key>
			<data>
			7YGcefNFUnGxGmxyl3IAycEX1ybkqOuEAtiE2J+EwIA=
			</data>
		</dict>
		<key>en.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			l91pc11VZkWXN5RfVh4tYkinNN0=
			</data>
			<key>hash2</key>
			<data>
			jtWntYqpC5MEA1du7VUeMjyMK9rshouteQYIWRu9y0s=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>en.lproj/MainInterface.strings</key>
		<dict>
			<key>hash</key>
			<data>
			BdnZmPuY2unYFbzwR6aEhWiD0LA=
			</data>
			<key>hash2</key>
			<data>
			kXXiLvkZczIykXc4ebIzCsdP2LHX+ACHNWj3PorGK0M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hans.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+J/SFbMCq8czy5xL/jdTbnoBjC8=
			</data>
			<key>hash2</key>
			<data>
			aH6awy7dGS7qDXBbiIXLbfnDAXd1dbSwvOg3AyjSchA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hans.lproj/MainInterface.strings</key>
		<dict>
			<key>hash</key>
			<data>
			BdnZmPuY2unYFbzwR6aEhWiD0LA=
			</data>
			<key>hash2</key>
			<data>
			kXXiLvkZczIykXc4ebIzCsdP2LHX+ACHNWj3PorGK0M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
