# SimLoc 1.8.6 - 修复版本

## 🎯 主要改进

这个版本解决了原版SimLoc导致被定位app卡死的问题，提供了更稳定和安全的模拟定位体验。

### ✅ 已修复的问题

1. **死锁问题** - 修复了Method Swizzling导致的递归调用死锁
2. **线程阻塞** - 实现了异步处理，避免主线程阻塞
3. **内存泄漏** - 改进了内存管理，防止内存泄漏
4. **兼容性问题** - 提高了与各种app的兼容性

### 🔧 技术改进

- **线程安全**: 使用NSLock和dispatch_queue确保线程安全
- **异步处理**: 位置计算在后台线程进行，不阻塞UI
- **错误处理**: 添加了完善的错误处理机制
- **资源管理**: 正确的构造和析构函数管理资源

## 📱 安装说明

### 方法一：使用AltStore
1. 下载 `SimLoc_Fixed_v1.8.6.ipa`
2. 在AltStore中选择"Install .ipa"
3. 选择下载的IPA文件进行安装

### 方法二：使用Sideloadly
1. 打开Sideloadly
2. 拖拽IPA文件到Sideloadly窗口
3. 输入Apple ID进行签名安装

### 方法三：使用3uTools
1. 连接设备到电脑
2. 在3uTools中选择"Install App"
3. 选择IPA文件进行安装

## ⚙️ 使用方法

1. **启动应用**: 安装后在桌面找到SimLoc图标
2. **设置位置**: 
   - 手动输入经纬度
   - 或使用地图选点功能
3. **启用模拟**: 点击"模拟定位"按钮
4. **测试效果**: 打开需要定位的app进行测试

## 🛡️ 安全提示

- 首次安装后需要在"设置 > 通用 > VPN与设备管理"中信任开发者证书
- 建议只在测试环境中使用，不要用于违法用途
- 使用前请备份重要数据

## 🔍 技术细节

### 核心改进
```objc
// 线程安全的位置设置
void setFakeLocation(double latitude, double longitude) {
    [g_locationLock lock];
    g_fakeCoordinate.latitude = latitude;
    g_fakeCoordinate.longitude = longitude;
    g_locationHookEnabled = (latitude != 0.0 || longitude != 0.0);
    [g_locationLock unlock];
}

// 异步处理避免阻塞
dispatch_sync(g_locationQueue, ^{
    CLLocationCoordinate2D fakeCoord = getFakeLocation();
    // 处理位置逻辑
});
```

### Hook机制
- 使用安全的Method Swizzling
- 保存原始方法指针避免递归
- 添加有效性检查

## 📋 版本历史

### v1.8.6 (当前版本)
- ✅ 修复死锁问题
- ✅ 实现线程安全
- ✅ 添加异步处理
- ✅ 改进错误处理

### v1.8.5 (原版本)
- ❌ 存在死锁问题
- ❌ 线程不安全
- ❌ 可能导致app卡死

## 🤝 支持与反馈

如果遇到问题或有改进建议，请通过以下方式联系：

- 检查设备兼容性（iOS 14.0+）
- 确认证书信任设置
- 重启设备后重试

## ⚠️ 免责声明

本软件仅供学习和测试使用，请遵守当地法律法规。开发者不对使用本软件造成的任何后果承担责任。

---

**享受更稳定的模拟定位体验！** 🎉
