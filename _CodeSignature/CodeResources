<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key><EMAIL></key>
		<data>
		/s43o9LtyInPGVMgPI7GMv5iUDQ=
		</data>
		<key>AppIcon76x76@2x~ipad.png</key>
		<data>
		3FbKZOdp1ThHUcmTUS8pk39HHLk=
		</data>
		<key>Assets.car</key>
		<data>
		/o6Tbm4iR4pmI4a5oBcDpw5Zf5s=
		</data>
		<key>Base.lproj/LaunchScreen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib</key>
		<data>
		tG+0NJBTDJU1pGyTkthn/3SEV7s=
		</data>
		<key>Base.lproj/LaunchScreen.storyboardc/Info.plist</key>
		<data>
		n2t8gsDpfE6XkhG31p7IQJRxTxU=
		</data>
		<key>Base.lproj/LaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib</key>
		<data>
		U0x8EM0781fCPb62BbZIXKeDlZ4=
		</data>
		<key>Base.lproj/Main.storyboardc/8yO-De-mAb-view-Oov-VX-oUb.nib</key>
		<data>
		8Ej099r6Xp7Z0/f/i8uyySaMjls=
		</data>
		<key>Base.lproj/Main.storyboardc/Info.plist</key>
		<data>
		U+pKBO7Znjlb9hnYEbJmyrZ2X6w=
		</data>
		<key>Base.lproj/Main.storyboardc/SKR-Th-G8B-view-iRn-lr-1rq.nib</key>
		<data>
		8Ej099r6Xp7Z0/f/i8uyySaMjls=
		</data>
		<key>Base.lproj/Main.storyboardc/TuR-3K-Wxd-view-TkH-xB-810.nib</key>
		<data>
		8Ej099r6Xp7Z0/f/i8uyySaMjls=
		</data>
		<key>Base.lproj/Main.storyboardc/UITabBarController-nop-DP-QgC.nib</key>
		<data>
		HQFyjAgBu2zZ9pL7AMP+MPP5VDY=
		</data>
		<key>Info.plist</key>
		<data>
		RUaIxbNGk6mMdGJpsrtvcUBdMlM=
		</data>
		<key>PkgInfo</key>
		<data>
		n57qDP4tZfLD1rCS43W0B4LQjzE=
		</data>
		<key>PlugIns/TAAction.appex/<EMAIL></key>
		<data>
		Nb6FY0A+bSskJaOdU8NapvCMTbg=
		</data>
		<key>PlugIns/TAAction.appex/AppIcon20x20@2x~ipad.png</key>
		<data>
		Nb6FY0A+bSskJaOdU8NapvCMTbg=
		</data>
		<key>PlugIns/TAAction.appex/<EMAIL></key>
		<data>
		rHENCkJTlqVayijXiPHHok80RD8=
		</data>
		<key>PlugIns/TAAction.appex/AppIcon20x20~ipad.png</key>
		<data>
		oKBW8VSWtvtMxudxVML+4kEbX0M=
		</data>
		<key>PlugIns/TAAction.appex/AppIcon29x29.png</key>
		<data>
		TiLcn2wWykB6NBSit/803bbx+bE=
		</data>
		<key>PlugIns/TAAction.appex/<EMAIL></key>
		<data>
		YmHPOZOVSfjGu7xJLWHriPRG9rw=
		</data>
		<key>PlugIns/TAAction.appex/AppIcon29x29@2x~ipad.png</key>
		<data>
		YmHPOZOVSfjGu7xJLWHriPRG9rw=
		</data>
		<key>PlugIns/TAAction.appex/<EMAIL></key>
		<data>
		4p0WG1k4WyAc59YTThtw0dvUiv0=
		</data>
		<key>PlugIns/TAAction.appex/AppIcon29x29~ipad.png</key>
		<data>
		TiLcn2wWykB6NBSit/803bbx+bE=
		</data>
		<key>PlugIns/TAAction.appex/<EMAIL></key>
		<data>
		gxW8LUcgwsN3GDtTAfExHt6CQl4=
		</data>
		<key>PlugIns/TAAction.appex/AppIcon40x40@2x~ipad.png</key>
		<data>
		gxW8LUcgwsN3GDtTAfExHt6CQl4=
		</data>
		<key>PlugIns/TAAction.appex/<EMAIL></key>
		<data>
		/s43o9LtyInPGVMgPI7GMv5iUDQ=
		</data>
		<key>PlugIns/TAAction.appex/AppIcon40x40~ipad.png</key>
		<data>
		Nb6FY0A+bSskJaOdU8NapvCMTbg=
		</data>
		<key>PlugIns/TAAction.appex/AppIcon50x50@2x~ipad.png</key>
		<data>
		fCNNa01TeAf+QWOuqpMi13p7LCc=
		</data>
		<key>PlugIns/TAAction.appex/AppIcon50x50~ipad.png</key>
		<data>
		cN0SfYmxDBDzaRM+FsqjascGYig=
		</data>
		<key>PlugIns/TAAction.appex/AppIcon57x57.png</key>
		<data>
		cjpPLjkO3a3QWH5eFUNFjHUW8uM=
		</data>
		<key>PlugIns/TAAction.appex/<EMAIL></key>
		<data>
		AZGATvmTXwmdIzci+aVaYQDo+V8=
		</data>
		<key>PlugIns/TAAction.appex/<EMAIL></key>
		<data>
		/s43o9LtyInPGVMgPI7GMv5iUDQ=
		</data>
		<key>PlugIns/TAAction.appex/<EMAIL></key>
		<data>
		sEbiksDvI5IS+uBkXcwPBQB7dxo=
		</data>
		<key>PlugIns/TAAction.appex/AppIcon72x72@2x~ipad.png</key>
		<data>
		70gRnJKHolWEt2r44Jtxno5/WOk=
		</data>
		<key>PlugIns/TAAction.appex/AppIcon72x72~ipad.png</key>
		<data>
		ht8NuExK4vz+uFgkgo4C5pf5ghg=
		</data>
		<key>PlugIns/TAAction.appex/AppIcon76x76@2x~ipad.png</key>
		<data>
		3FbKZOdp1ThHUcmTUS8pk39HHLk=
		</data>
		<key>PlugIns/TAAction.appex/AppIcon76x76~ipad.png</key>
		<data>
		ADxNtKNHi+p6A1JdMptP++IxcoQ=
		</data>
		<key>PlugIns/TAAction.appex/AppIcon83.5x83.5@2x~ipad.png</key>
		<data>
		5CEggOTBqxE9zb1uC1GmgMj9eY0=
		</data>
		<key>PlugIns/TAAction.appex/Assets.car</key>
		<data>
		/o6Tbm4iR4pmI4a5oBcDpw5Zf5s=
		</data>
		<key>PlugIns/TAAction.appex/Base.lproj/MainInterface.storyboardc/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			LphSzypO58Ee41BVLe8F3DJZUps=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/TAAction.appex/Base.lproj/MainInterface.storyboardc/ObA-dk-sSI-view-zMn-AG-sqS.nib</key>
		<dict>
			<key>hash</key>
			<data>
			0IjWpysJaGkLVF9oDzzXtVJ9ktU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/TAAction.appex/Base.lproj/MainInterface.storyboardc/UIViewController-ObA-dk-sSI.nib</key>
		<dict>
			<key>hash</key>
			<data>
			eB7HT+rTuqAbLBK/aoll4ugnL/o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/TAAction.appex/Info.plist</key>
		<data>
		xvKMODuDa+3t8oaDlKMB7wQxWC8=
		</data>
		<key>PlugIns/TAAction.appex/TAAction</key>
		<data>
		LEUecZRpKYMRB5qW2ycFl4BxXnE=
		</data>
		<key>PlugIns/TAAction.appex/_CodeSignature/CodeResources</key>
		<data>
		w4IXc1a/49ARLeWJTcmtu7FadUQ=
		</data>
		<key>PlugIns/TAAction.appex/en.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			l91pc11VZkWXN5RfVh4tYkinNN0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/TAAction.appex/en.lproj/MainInterface.strings</key>
		<dict>
			<key>hash</key>
			<data>
			BdnZmPuY2unYFbzwR6aEhWiD0LA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/TAAction.appex/zh-Hans.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+J/SFbMCq8czy5xL/jdTbnoBjC8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/TAAction.appex/zh-Hans.lproj/MainInterface.strings</key>
		<dict>
			<key>hash</key>
			<data>
			BdnZmPuY2unYFbzwR6aEhWiD0LA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>SVProgressHUD.bundle/angle-mask.png</key>
		<data>
		oqIJzVxhZua/CZX+TGK3kylFilY=
		</data>
		<key>SVProgressHUD.bundle/<EMAIL></key>
		<data>
		3aAb8SeX29Ro7z1pt6A1yqUAaHs=
		</data>
		<key>SVProgressHUD.bundle/<EMAIL></key>
		<data>
		pPD0GLNI2PnWq2CX6UyQ9H52T9g=
		</data>
		<key>SVProgressHUD.bundle/error.png</key>
		<data>
		oP6f/Zad0pEL8xrimTdNlSXrrpA=
		</data>
		<key>SVProgressHUD.bundle/<EMAIL></key>
		<data>
		cFCuvbmEkaKmhj2RThSqw/5KUtM=
		</data>
		<key>SVProgressHUD.bundle/<EMAIL></key>
		<data>
		QSlxhLYpqk8M4tlq+X0hXbamUH0=
		</data>
		<key>SVProgressHUD.bundle/info.png</key>
		<data>
		CP1bXzRkp6v+a6oAHECMgzhmOIU=
		</data>
		<key>SVProgressHUD.bundle/<EMAIL></key>
		<data>
		yx+ZQImmZJ+j/av7X/uJpzaqomU=
		</data>
		<key>SVProgressHUD.bundle/<EMAIL></key>
		<data>
		5MgHR68ek5Ayg9vc2oGqk1wMpdQ=
		</data>
		<key>SVProgressHUD.bundle/success.png</key>
		<data>
		oQ/IlYYGw5vH/rx4pqvYiNddu8c=
		</data>
		<key>SVProgressHUD.bundle/<EMAIL></key>
		<data>
		P1jXd1XGKWq/KG50KF6pWjBVwBo=
		</data>
		<key>SVProgressHUD.bundle/<EMAIL></key>
		<data>
		hmliT+WZg212OTNmAhP+uSOyYio=
		</data>
		<key>en.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			zmV6UqBSo6r1NOz798vd5O4zTBA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>en.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Kbgn0T+/MtTpFJ6AfH8MnMSjTxU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>en.lproj/Main.strings</key>
		<dict>
			<key>hash</key>
			<data>
			bqfdPXZReoan5S5CCTH37R2qVK8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>macked.app.dylib</key>
		<data>
		aEJezt6a6a783UOYriRUdASej2o=
		</data>
		<key>zh-Hans.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			uHlL9irLGrw4aqaQn2RXqlQHrGY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hans.lproj/LaunchScreen.strings</key>
		<dict>
			<key>hash</key>
			<data>
			rcg7GeeTSRscbqD9i0bNnzLlkvw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hans.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			9uExNooCmuJl7Ez/SN5Y40J2SE4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hans.lproj/Main.strings</key>
		<dict>
			<key>hash</key>
			<data>
			yrUZU0x3r1LSRS56WSWcM6AjMxM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>files2</key>
	<dict>
		<key><EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			/s43o9LtyInPGVMgPI7GMv5iUDQ=
			</data>
			<key>hash2</key>
			<data>
			8dfxyVLFscN+7gIDw/wLFv9qd565n84m4prOvQ+5VCA=
			</data>
		</dict>
		<key>AppIcon76x76@2x~ipad.png</key>
		<dict>
			<key>hash</key>
			<data>
			3FbKZOdp1ThHUcmTUS8pk39HHLk=
			</data>
			<key>hash2</key>
			<data>
			pd6jc2toSvCEfXhJCgmBvZvX32GdhV86Cg50Q7tKGjc=
			</data>
		</dict>
		<key>Assets.car</key>
		<dict>
			<key>hash</key>
			<data>
			/o6Tbm4iR4pmI4a5oBcDpw5Zf5s=
			</data>
			<key>hash2</key>
			<data>
			KnImrGMza6SG6i06trB4XsQZd2w2H4sQLmWh+n7O8uA=
			</data>
		</dict>
		<key>Base.lproj/LaunchScreen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib</key>
		<dict>
			<key>hash</key>
			<data>
			tG+0NJBTDJU1pGyTkthn/3SEV7s=
			</data>
			<key>hash2</key>
			<data>
			eGOOA9v7cm8vfK6MRci5Cka6ymlf1C3AyF6Gqr5XqPo=
			</data>
		</dict>
		<key>Base.lproj/LaunchScreen.storyboardc/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			n2t8gsDpfE6XkhG31p7IQJRxTxU=
			</data>
			<key>hash2</key>
			<data>
			HyVdXMU7Ux4/KalAao30mpWOK/lEPT4gvYN09wf31cg=
			</data>
		</dict>
		<key>Base.lproj/LaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib</key>
		<dict>
			<key>hash</key>
			<data>
			U0x8EM0781fCPb62BbZIXKeDlZ4=
			</data>
			<key>hash2</key>
			<data>
			cWq7q+iQcmpSoaFGecnyPdKFEPFwRD3slgUmxFADkVs=
			</data>
		</dict>
		<key>Base.lproj/Main.storyboardc/8yO-De-mAb-view-Oov-VX-oUb.nib</key>
		<dict>
			<key>hash</key>
			<data>
			8Ej099r6Xp7Z0/f/i8uyySaMjls=
			</data>
			<key>hash2</key>
			<data>
			ahd1W02AZRnu5OA8hpuDfk84k94PNjX+yCiopcpB6fE=
			</data>
		</dict>
		<key>Base.lproj/Main.storyboardc/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			U+pKBO7Znjlb9hnYEbJmyrZ2X6w=
			</data>
			<key>hash2</key>
			<data>
			2/17oekhWAwq1Wa5v59Lv4US3S9MCyzt3wqTI6/Bnw0=
			</data>
		</dict>
		<key>Base.lproj/Main.storyboardc/SKR-Th-G8B-view-iRn-lr-1rq.nib</key>
		<dict>
			<key>hash</key>
			<data>
			8Ej099r6Xp7Z0/f/i8uyySaMjls=
			</data>
			<key>hash2</key>
			<data>
			ahd1W02AZRnu5OA8hpuDfk84k94PNjX+yCiopcpB6fE=
			</data>
		</dict>
		<key>Base.lproj/Main.storyboardc/TuR-3K-Wxd-view-TkH-xB-810.nib</key>
		<dict>
			<key>hash</key>
			<data>
			8Ej099r6Xp7Z0/f/i8uyySaMjls=
			</data>
			<key>hash2</key>
			<data>
			ahd1W02AZRnu5OA8hpuDfk84k94PNjX+yCiopcpB6fE=
			</data>
		</dict>
		<key>Base.lproj/Main.storyboardc/UITabBarController-nop-DP-QgC.nib</key>
		<dict>
			<key>hash</key>
			<data>
			HQFyjAgBu2zZ9pL7AMP+MPP5VDY=
			</data>
			<key>hash2</key>
			<data>
			483vPuKQNaTwbLVP3zVSnQ3wkgbgt/24ofGPCf9su9s=
			</data>
		</dict>
		<key>PlugIns/TAAction.appex/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			Nb6FY0A+bSskJaOdU8NapvCMTbg=
			</data>
			<key>hash2</key>
			<data>
			kfGV5toF0b660WRhEddj3nWty6WRoCPdhh32TJKDfvs=
			</data>
		</dict>
		<key>PlugIns/TAAction.appex/AppIcon20x20@2x~ipad.png</key>
		<dict>
			<key>hash</key>
			<data>
			Nb6FY0A+bSskJaOdU8NapvCMTbg=
			</data>
			<key>hash2</key>
			<data>
			kfGV5toF0b660WRhEddj3nWty6WRoCPdhh32TJKDfvs=
			</data>
		</dict>
		<key>PlugIns/TAAction.appex/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			rHENCkJTlqVayijXiPHHok80RD8=
			</data>
			<key>hash2</key>
			<data>
			hDlnSbpJzo0mJ4vxq0pB0mLFNYpFgrx7HuT75OIEscY=
			</data>
		</dict>
		<key>PlugIns/TAAction.appex/AppIcon20x20~ipad.png</key>
		<dict>
			<key>hash</key>
			<data>
			oKBW8VSWtvtMxudxVML+4kEbX0M=
			</data>
			<key>hash2</key>
			<data>
			79K+LgFsFnrjpaPukI8LwA72bAUQpXANbNpmCvAFv8g=
			</data>
		</dict>
		<key>PlugIns/TAAction.appex/AppIcon29x29.png</key>
		<dict>
			<key>hash</key>
			<data>
			TiLcn2wWykB6NBSit/803bbx+bE=
			</data>
			<key>hash2</key>
			<data>
			B9GZ6rsBJ9/cwYgd7m3Kj6T5/IQt0qNi4ABZkhUc0R0=
			</data>
		</dict>
		<key>PlugIns/TAAction.appex/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			YmHPOZOVSfjGu7xJLWHriPRG9rw=
			</data>
			<key>hash2</key>
			<data>
			Z0lUiyNCFFPZ8MCsRs3kJ5EoNGc5us29GVsjrjbwjKQ=
			</data>
		</dict>
		<key>PlugIns/TAAction.appex/AppIcon29x29@2x~ipad.png</key>
		<dict>
			<key>hash</key>
			<data>
			YmHPOZOVSfjGu7xJLWHriPRG9rw=
			</data>
			<key>hash2</key>
			<data>
			Z0lUiyNCFFPZ8MCsRs3kJ5EoNGc5us29GVsjrjbwjKQ=
			</data>
		</dict>
		<key>PlugIns/TAAction.appex/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			4p0WG1k4WyAc59YTThtw0dvUiv0=
			</data>
			<key>hash2</key>
			<data>
			cNNBymOmHlrszVkZgHBpW73RGMBoiga/K3qfcjZSbwg=
			</data>
		</dict>
		<key>PlugIns/TAAction.appex/AppIcon29x29~ipad.png</key>
		<dict>
			<key>hash</key>
			<data>
			TiLcn2wWykB6NBSit/803bbx+bE=
			</data>
			<key>hash2</key>
			<data>
			B9GZ6rsBJ9/cwYgd7m3Kj6T5/IQt0qNi4ABZkhUc0R0=
			</data>
		</dict>
		<key>PlugIns/TAAction.appex/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			gxW8LUcgwsN3GDtTAfExHt6CQl4=
			</data>
			<key>hash2</key>
			<data>
			zeBXAgr51pBAjOUyiLbSXW4rdt94QjwaePOSSGN9MoI=
			</data>
		</dict>
		<key>PlugIns/TAAction.appex/AppIcon40x40@2x~ipad.png</key>
		<dict>
			<key>hash</key>
			<data>
			gxW8LUcgwsN3GDtTAfExHt6CQl4=
			</data>
			<key>hash2</key>
			<data>
			zeBXAgr51pBAjOUyiLbSXW4rdt94QjwaePOSSGN9MoI=
			</data>
		</dict>
		<key>PlugIns/TAAction.appex/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			/s43o9LtyInPGVMgPI7GMv5iUDQ=
			</data>
			<key>hash2</key>
			<data>
			8dfxyVLFscN+7gIDw/wLFv9qd565n84m4prOvQ+5VCA=
			</data>
		</dict>
		<key>PlugIns/TAAction.appex/AppIcon40x40~ipad.png</key>
		<dict>
			<key>hash</key>
			<data>
			Nb6FY0A+bSskJaOdU8NapvCMTbg=
			</data>
			<key>hash2</key>
			<data>
			kfGV5toF0b660WRhEddj3nWty6WRoCPdhh32TJKDfvs=
			</data>
		</dict>
		<key>PlugIns/TAAction.appex/AppIcon50x50@2x~ipad.png</key>
		<dict>
			<key>hash</key>
			<data>
			fCNNa01TeAf+QWOuqpMi13p7LCc=
			</data>
			<key>hash2</key>
			<data>
			ksUeor/lSPhOKX7CODEN8eapSHquI5QCkA230wobrLw=
			</data>
		</dict>
		<key>PlugIns/TAAction.appex/AppIcon50x50~ipad.png</key>
		<dict>
			<key>hash</key>
			<data>
			cN0SfYmxDBDzaRM+FsqjascGYig=
			</data>
			<key>hash2</key>
			<data>
			4c8w0PDU8mG9OopdEebxJayGiC8ykDokaAuSb/o0Zxo=
			</data>
		</dict>
		<key>PlugIns/TAAction.appex/AppIcon57x57.png</key>
		<dict>
			<key>hash</key>
			<data>
			cjpPLjkO3a3QWH5eFUNFjHUW8uM=
			</data>
			<key>hash2</key>
			<data>
			/l9lUo4SAtAgF458m3fIFinwOYmqki1G+NNljC1KzTs=
			</data>
		</dict>
		<key>PlugIns/TAAction.appex/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			AZGATvmTXwmdIzci+aVaYQDo+V8=
			</data>
			<key>hash2</key>
			<data>
			eMd2xnkRx9jwSewX1qPX7ptOrygK5r1I+nIo+HhvX0s=
			</data>
		</dict>
		<key>PlugIns/TAAction.appex/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			/s43o9LtyInPGVMgPI7GMv5iUDQ=
			</data>
			<key>hash2</key>
			<data>
			8dfxyVLFscN+7gIDw/wLFv9qd565n84m4prOvQ+5VCA=
			</data>
		</dict>
		<key>PlugIns/TAAction.appex/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			sEbiksDvI5IS+uBkXcwPBQB7dxo=
			</data>
			<key>hash2</key>
			<data>
			O268Sj5bS8tUCXt62WtVBkOjNo7hQpAiAXRC6jVKH18=
			</data>
		</dict>
		<key>PlugIns/TAAction.appex/AppIcon72x72@2x~ipad.png</key>
		<dict>
			<key>hash</key>
			<data>
			70gRnJKHolWEt2r44Jtxno5/WOk=
			</data>
			<key>hash2</key>
			<data>
			BS/9OYj5HTn7dPAewCxX8mYEa+vJFmTMgJLWyeEsl64=
			</data>
		</dict>
		<key>PlugIns/TAAction.appex/AppIcon72x72~ipad.png</key>
		<dict>
			<key>hash</key>
			<data>
			ht8NuExK4vz+uFgkgo4C5pf5ghg=
			</data>
			<key>hash2</key>
			<data>
			Y5Nu1nV8E/Rcmz8f5P5Ts66Lhpq80KfUKwZ1mpi6nF0=
			</data>
		</dict>
		<key>PlugIns/TAAction.appex/AppIcon76x76@2x~ipad.png</key>
		<dict>
			<key>hash</key>
			<data>
			3FbKZOdp1ThHUcmTUS8pk39HHLk=
			</data>
			<key>hash2</key>
			<data>
			pd6jc2toSvCEfXhJCgmBvZvX32GdhV86Cg50Q7tKGjc=
			</data>
		</dict>
		<key>PlugIns/TAAction.appex/AppIcon76x76~ipad.png</key>
		<dict>
			<key>hash</key>
			<data>
			ADxNtKNHi+p6A1JdMptP++IxcoQ=
			</data>
			<key>hash2</key>
			<data>
			I/AXLxtWfWpL9qajZJM5gfR+nMVKwFljbsTMagjDSyw=
			</data>
		</dict>
		<key>PlugIns/TAAction.appex/AppIcon83.5x83.5@2x~ipad.png</key>
		<dict>
			<key>hash</key>
			<data>
			5CEggOTBqxE9zb1uC1GmgMj9eY0=
			</data>
			<key>hash2</key>
			<data>
			PsorPREleNrd8qDloTTWYkhByqQkC3lW3HTx1x7/ra4=
			</data>
		</dict>
		<key>PlugIns/TAAction.appex/Assets.car</key>
		<dict>
			<key>hash</key>
			<data>
			/o6Tbm4iR4pmI4a5oBcDpw5Zf5s=
			</data>
			<key>hash2</key>
			<data>
			KnImrGMza6SG6i06trB4XsQZd2w2H4sQLmWh+n7O8uA=
			</data>
		</dict>
		<key>PlugIns/TAAction.appex/Base.lproj/MainInterface.storyboardc/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			LphSzypO58Ee41BVLe8F3DJZUps=
			</data>
			<key>hash2</key>
			<data>
			9Zkoj+a7Nv+GT39rrbg05J2Y7OQHhbaZkFLZo44H0so=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/TAAction.appex/Base.lproj/MainInterface.storyboardc/ObA-dk-sSI-view-zMn-AG-sqS.nib</key>
		<dict>
			<key>hash</key>
			<data>
			0IjWpysJaGkLVF9oDzzXtVJ9ktU=
			</data>
			<key>hash2</key>
			<data>
			bzXaRcFvlbhZQxUCWojp3BdTYV5z2jwuQ6OZY2MtWR4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/TAAction.appex/Base.lproj/MainInterface.storyboardc/UIViewController-ObA-dk-sSI.nib</key>
		<dict>
			<key>hash</key>
			<data>
			eB7HT+rTuqAbLBK/aoll4ugnL/o=
			</data>
			<key>hash2</key>
			<data>
			7YGcefNFUnGxGmxyl3IAycEX1ybkqOuEAtiE2J+EwIA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/TAAction.appex/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			xvKMODuDa+3t8oaDlKMB7wQxWC8=
			</data>
			<key>hash2</key>
			<data>
			hs5pGlET68Lr6tLtcwoetmetrh2xjSVqPaZk3KtcATs=
			</data>
		</dict>
		<key>PlugIns/TAAction.appex/TAAction</key>
		<dict>
			<key>hash</key>
			<data>
			LEUecZRpKYMRB5qW2ycFl4BxXnE=
			</data>
			<key>hash2</key>
			<data>
			D/lZeRzT+3LznBTuhNkzJHDk2L8Mdzx8Sj/ZRcDC3+g=
			</data>
		</dict>
		<key>PlugIns/TAAction.appex/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash</key>
			<data>
			w4IXc1a/49ARLeWJTcmtu7FadUQ=
			</data>
			<key>hash2</key>
			<data>
			wdJ/On4q5qytvfVwuY0ODa38Ywmy0bcdq7HxtIZvWrs=
			</data>
		</dict>
		<key>PlugIns/TAAction.appex/en.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			l91pc11VZkWXN5RfVh4tYkinNN0=
			</data>
			<key>hash2</key>
			<data>
			jtWntYqpC5MEA1du7VUeMjyMK9rshouteQYIWRu9y0s=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/TAAction.appex/en.lproj/MainInterface.strings</key>
		<dict>
			<key>hash</key>
			<data>
			BdnZmPuY2unYFbzwR6aEhWiD0LA=
			</data>
			<key>hash2</key>
			<data>
			kXXiLvkZczIykXc4ebIzCsdP2LHX+ACHNWj3PorGK0M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/TAAction.appex/zh-Hans.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+J/SFbMCq8czy5xL/jdTbnoBjC8=
			</data>
			<key>hash2</key>
			<data>
			aH6awy7dGS7qDXBbiIXLbfnDAXd1dbSwvOg3AyjSchA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>PlugIns/TAAction.appex/zh-Hans.lproj/MainInterface.strings</key>
		<dict>
			<key>hash</key>
			<data>
			BdnZmPuY2unYFbzwR6aEhWiD0LA=
			</data>
			<key>hash2</key>
			<data>
			kXXiLvkZczIykXc4ebIzCsdP2LHX+ACHNWj3PorGK0M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>SVProgressHUD.bundle/angle-mask.png</key>
		<dict>
			<key>hash</key>
			<data>
			oqIJzVxhZua/CZX+TGK3kylFilY=
			</data>
			<key>hash2</key>
			<data>
			DDw2jnXp1WjofT+IkD6qy3+dLIzDE9vrxNSPoeQO4jo=
			</data>
		</dict>
		<key>SVProgressHUD.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			3aAb8SeX29Ro7z1pt6A1yqUAaHs=
			</data>
			<key>hash2</key>
			<data>
			6QAJXoYU7vQpTdzUcb2HrKhSj6gCcrZb9r6z0J5yK+w=
			</data>
		</dict>
		<key>SVProgressHUD.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			pPD0GLNI2PnWq2CX6UyQ9H52T9g=
			</data>
			<key>hash2</key>
			<data>
			iwwAX8HeeU70vQudb1L6z80VbnuVeJ+zn94jLMovmKs=
			</data>
		</dict>
		<key>SVProgressHUD.bundle/error.png</key>
		<dict>
			<key>hash</key>
			<data>
			oP6f/Zad0pEL8xrimTdNlSXrrpA=
			</data>
			<key>hash2</key>
			<data>
			UDk/X+yyN+USnDW9JFp7P9O5irCLZkeybVB1BEDVunA=
			</data>
		</dict>
		<key>SVProgressHUD.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			cFCuvbmEkaKmhj2RThSqw/5KUtM=
			</data>
			<key>hash2</key>
			<data>
			Xc9fgzh3g2FX8j9dYqux/gm+73IDul7Vb0KaMAQZo+E=
			</data>
		</dict>
		<key>SVProgressHUD.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			QSlxhLYpqk8M4tlq+X0hXbamUH0=
			</data>
			<key>hash2</key>
			<data>
			P6wK1oxOL6vyD7o9q9+hP9jctZaTyluZgm1zpKgw0dM=
			</data>
		</dict>
		<key>SVProgressHUD.bundle/info.png</key>
		<dict>
			<key>hash</key>
			<data>
			CP1bXzRkp6v+a6oAHECMgzhmOIU=
			</data>
			<key>hash2</key>
			<data>
			GeypxgyrUdKQ8Nyqc9w0q9XLCdtRbk4cJi9EE5QXXs8=
			</data>
		</dict>
		<key>SVProgressHUD.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			yx+ZQImmZJ+j/av7X/uJpzaqomU=
			</data>
			<key>hash2</key>
			<data>
			KLsrQZrjUwrsUtZSul1bJbNH8OTb5+YrjaVW+rbl64Q=
			</data>
		</dict>
		<key>SVProgressHUD.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			5MgHR68ek5Ayg9vc2oGqk1wMpdQ=
			</data>
			<key>hash2</key>
			<data>
			eer51e9B3uVrPmnlKfYiqok7LLlR6cnUt8JBmo2oG3Y=
			</data>
		</dict>
		<key>SVProgressHUD.bundle/success.png</key>
		<dict>
			<key>hash</key>
			<data>
			oQ/IlYYGw5vH/rx4pqvYiNddu8c=
			</data>
			<key>hash2</key>
			<data>
			ZneDtnotiia14nbyRklrCVgGmnlPcjpSwUjyHSfPeXc=
			</data>
		</dict>
		<key>SVProgressHUD.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			P1jXd1XGKWq/KG50KF6pWjBVwBo=
			</data>
			<key>hash2</key>
			<data>
			hbpOiBtrYWqPZJYnEBIf/dZWn+6rFwHRPp0WzU9gA18=
			</data>
		</dict>
		<key>SVProgressHUD.bundle/<EMAIL></key>
		<dict>
			<key>hash</key>
			<data>
			hmliT+WZg212OTNmAhP+uSOyYio=
			</data>
			<key>hash2</key>
			<data>
			EMic5PZw5ab7sBUjL5t1YMrH5WnRAJCbgeMLFJgZQ/Q=
			</data>
		</dict>
		<key>en.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			zmV6UqBSo6r1NOz798vd5O4zTBA=
			</data>
			<key>hash2</key>
			<data>
			kmHsztpgjvF0JW5f3HdMHm49z1M0CcG8OT1JDQHHE/E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>en.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Kbgn0T+/MtTpFJ6AfH8MnMSjTxU=
			</data>
			<key>hash2</key>
			<data>
			7WBSvAY4KPhYMfpJJh8w87kFwTa17rLVXnqWrR11ywo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>en.lproj/Main.strings</key>
		<dict>
			<key>hash</key>
			<data>
			bqfdPXZReoan5S5CCTH37R2qVK8=
			</data>
			<key>hash2</key>
			<data>
			pEXTZHmwN9hy375f1BRmDA7OKzjxGv4fKEHmAhJjjag=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>macked.app.dylib</key>
		<dict>
			<key>hash</key>
			<data>
			aEJezt6a6a783UOYriRUdASej2o=
			</data>
			<key>hash2</key>
			<data>
			TVBS6q+J+Mk+T2RDvOPzZbvZ4md6vohmzUzxRxgZBw8=
			</data>
		</dict>
		<key>zh-Hans.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			uHlL9irLGrw4aqaQn2RXqlQHrGY=
			</data>
			<key>hash2</key>
			<data>
			z+qDihpDph3IlytwEw4rzl6Xkel/kyjlXT6I5j2mzPA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hans.lproj/LaunchScreen.strings</key>
		<dict>
			<key>hash</key>
			<data>
			rcg7GeeTSRscbqD9i0bNnzLlkvw=
			</data>
			<key>hash2</key>
			<data>
			AbpHGcgLb+kRsJGnwFEktk7uzpZOCcBY74+YBdrKVGs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hans.lproj/Localizable.strings</key>
		<dict>
			<key>hash</key>
			<data>
			9uExNooCmuJl7Ez/SN5Y40J2SE4=
			</data>
			<key>hash2</key>
			<data>
			JUBhBeme4fojNWgV/SO5uShMMZARfkjbZu1zdPcdDZg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>zh-Hans.lproj/Main.strings</key>
		<dict>
			<key>hash</key>
			<data>
			yrUZU0x3r1LSRS56WSWcM6AjMxM=
			</data>
			<key>hash2</key>
			<data>
			t9W2YED0LjeLKPmulLuqhHNuUt59REAHUJdYaoTHkq4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
